# Episode Badge Fix - Documentation

## সমস্যা
আগে সব links এ Episode badge দেখাচ্ছিল, কিন্তু এখন শুধুমাত্র Episode Links Generator দিয়ে তৈরি করা links এ Episode badge দেখানো উচিত।

## সমাধান
আমি `_is_episode_link` meta field ব্যবহার করে Episode links এবং Normal links আলাদা করেছি।

## কীভাবে কাজ করে

### ১. Episode Links Generator:
- যখন Episode Links Generator দিয়ে links তৈরি করবেন
- Automatically `_is_episode_link = true` meta field set হবে
- এই links গুলোতে Episode badge (EP-1, EP-2) দেখাবে

### ২. Normal Links:
- যখন "Add Links" button দিয়ে normal links add করবেন
- `_is_episode_link` meta field set হবে না
- এই links গুলোতে Episode badge দেখাবে না

### ৩. Frontend Display:
- **Episode Links আছে**: Episode column দেখাবে
- **শুধু Normal Links**: Episode column দেখাবে না
- **Mixed Links**: Episode column দেখাবে, episode links এ badge, normal links এ "DOWNLOAD"

## Test করার জন্য

### Step 1: Episode Links তৈরি করুন
1. Episode Links Generator ব্যবহার করুন
2. 3টি episode links তৈরি করুন
3. Admin panel এ EP-1, EP-2, EP-3 badge দেখুন

### Step 2: Normal Link যুক্ত করুন
1. "Add Links" button ব্যবহার করুন
2. একটি normal download link add করুন
3. এই link এ Episode badge দেখাবে না

### Step 3: Frontend Check করুন
1. Frontend এ গিয়ে links table দেখুন
2. Episode column দেখাবে
3. Episode links এ EP-1, EP-2 badge
4. Normal link এ "DOWNLOAD" badge

## Existing Links Fix করার জন্য

### Option 1: PHP Script (Recommended)
```php
// fix-existing-links.php file run করুন
// এটি automatically existing links fix করে দেবে
```

### Option 2: Manual Fix
1. Admin panel এ যান
2. Existing links গুলো edit করুন
3. Episode links গুলোর title EP-1, EP-2 format এ রাখুন
4. Normal links গুলোর title অন্য কিছু রাখুন

## Code Changes Summary

### Files Modified:
1. `inc/doo_links.php` - Episode detection logic
2. `inc/parts/single/links.php` - Frontend display logic
3. Episode Links Generator - Auto-marking episode links

### Database Changes:
- `_is_episode_link` meta field যুক্ত করা হয়েছে
- Episode links এ `_is_episode_link = true`
- Normal links এ এই field থাকবে না

### Logic Flow:
```
Episode Links Generator → _is_episode_link = true → Episode Badge
Normal Add Links → No meta field → No Episode Badge
```

## Visual Indicators

### Episode Links:
- **Admin**: Purple badge with EP-1, EP-2
- **Frontend**: Purple gradient badge with episode number

### Normal Links:
- **Admin**: No episode badge, just server name
- **Frontend**: Blue "DOWNLOAD" badge (if mixed with episodes)

## Troubleshooting

### যদি সব links এ Episode badge দেখায়:
1. `fix-existing-links.php` script run করুন
2. অথবা manually existing links এর title change করুন

### যদি Episode links এ badge না দেখায়:
1. Episode Links Generator ব্যবহার করে নতুন links তৈরি করুন
2. Check করুন যে title EP-1, EP-2 format এ আছে কিনা

### যদি Frontend এ table structure ভেঙে যায়:
1. Browser cache clear করুন
2. CSS properly loaded আছে কিনা check করুন

এখন Episode Links Generator এবং Normal Links আলাদাভাবে কাজ করবে! 🎯

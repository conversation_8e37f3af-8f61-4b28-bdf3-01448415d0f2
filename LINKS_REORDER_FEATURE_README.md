# Links Drag & Drop Reordering Feature - Documentation

## Overview
এই নতুন feature টি আপনাকে download links গুলোর order সহজেই change করার সুবিধা দেয়। Video Player এর মতোই drag & drop এবং up/down arrow দিয়ে links reorder করা যায়।

## Features
- **Drag & Drop**: Mouse দিয়ে drag করে links reorder করা যায়
- **Arrow Controls**: Up/Down arrow buttons দিয়ে links move করা যায়
- **Auto Save**: Order change হলে automatically database এ save হয়ে যায়
- **Visual Feedback**: Order update হলে success/error message দেখায়
- **Responsive Design**: Mobile এবং desktop উভয়ে কাজ করে

## কীভাবে ব্যবহার করবেন

### ১. Drag & Drop Method:
1. **WordPress Admin → Movies/Episodes → Edit Post** এ যান
2. **Links table** এ **move icon** (⋮⋮) দেখতে পাবেন
3. **Move icon এ click করে drag** করুন
4. **যেখানে রাখতে চান সেখানে drop** করুন
5. **Automatically order save** হয়ে যাবে

### ২. Arrow Controls Method:
1. **Links table** এ **up/down arrows** (↑↓) দেখতে পাবেন
2. **Up arrow** click করলে link উপরে যাবে
3. **Down arrow** click করলে link নিচে যাবে
4. **Automatically order save** হয়ে যাবে

### ৩. Visual Indicators:
- **Move Icon (⋮⋮)**: Drag handle - এটি দিয়ে drag করুন
- **Up Arrow (↑)**: Link কে উপরে move করে
- **Down Arrow (↓)**: Link কে নিচে move করে
- **Success Message**: "Order updated!" - সবুজ color এ
- **Error Message**: "Error updating order" - লাল color এ

## Technical Details

### Files Modified/Created:
1. `inc/doo_links.php` - AJAX handler এবং table structure updated
2. `inc/parts/links_editor.php` - Table header এ Order column যুক্ত
3. `inc/doo_scripts.php` - JavaScript file enqueued
4. `dooplay/assets/js/links-reorder.js` - Main functionality
5. Database: `menu_order` field ব্যবহার করে sorting

### Database Changes:
- **`menu_order` field**: Links এর order store করার জন্য
- **SQL Query**: `ORDER BY menu_order ASC, ID DESC` - order অনুযায়ী display

### AJAX Endpoint:
- **Action**: `save_links_order`
- **Method**: POST
- **Data**: Links array with ID and order
- **Security**: User capability check

## Browser Compatibility
- **Desktop**: Chrome, Firefox, Safari, Edge
- **Mobile**: Touch-friendly drag & drop
- **jQuery UI**: Sortable functionality ব্যবহার করে

## Styling Features

### Visual Elements:
- **Drag Handle**: Gray color, hover এ blue হয়
- **Arrow Buttons**: Blue color, hover এ background change
- **Placeholder**: Dashed blue border যখন drag করবেন
- **Helper**: Shadow effect drag করার সময়

### Responsive Design:
- **Desktop**: Horizontal layout (drag handle + arrows)
- **Mobile**: Vertical layout for better touch experience
- **Disabled States**: First row এ up arrow disabled, last row এ down arrow disabled

## Usage Examples

### Example 1: Episode Links Reordering
```
Before:
EP-3 | Download Link 3
EP-1 | Download Link 1  
EP-2 | Download Link 2

After Reordering:
EP-1 | Download Link 1
EP-2 | Download Link 2
EP-3 | Download Link 3
```

### Example 2: Quality Priority
```
Before:
HD 720p | Link 1
HD 1080p | Link 2
HD 480p | Link 3

After Reordering (Best quality first):
HD 1080p | Link 2
HD 720p | Link 1
HD 480p | Link 3
```

## Troubleshooting

### যদি Drag & Drop কাজ না করে:
1. **jQuery UI Sortable** properly loaded আছে কিনা check করুন
2. **Browser console** এ error আছে কিনা দেখুন
3. **JavaScript file** properly enqueued আছে কিনা verify করুন

### যদি Order Save না হয়:
1. **User permission** check করুন (subscriber বা higher)
2. **AJAX URL** সঠিক আছে কিনা verify করুন
3. **Network tab** এ AJAX request success হচ্ছে কিনা দেখুন

### যদি Visual feedback না দেখায়:
1. **CSS properly loaded** আছে কিনা check করুন
2. **Z-index conflicts** আছে কিনা দেখুন
3. **Browser cache clear** করুন

## Performance Considerations
- **Lightweight**: শুধু post edit pages এ load হয়
- **Efficient AJAX**: Batch update করে database calls minimize করে
- **Optimized CSS**: Inline CSS ব্যবহার করে external file dependency কম
- **Memory Friendly**: Event delegation ব্যবহার করে memory leaks prevent করে

## Future Enhancements
- **Bulk Selection**: Multiple links একসাথে move করা
- **Keyboard Shortcuts**: Ctrl+Up/Down দিয়ে reorder
- **Undo/Redo**: Order changes undo করার option
- **Custom Sorting**: Date, size, quality অনুযায়ী auto-sort
- **Import/Export**: Order configuration save/load করা

## Integration with Episode Links Generator
- **Auto-numbering**: Episode links automatically proper order এ create হয়
- **Seamless Integration**: Episode generator এর সাথে reorder feature perfectly কাজ করে
- **Consistent UI**: Same design language এবং user experience

এখন আপনি video player এর মতোই download links গুলোও সহজেই drag & drop বা arrow controls দিয়ে reorder করতে পারবেন! 🎯

jQuery(document).ready(function($) {

    console.log('Links Reorder Script Loaded');

    // Initialize sortable functionality
    function initLinksSortable() {
        if ($('#doolinks_response').length) {
            $('#doolinks_response').sortable({
                items: '.link-row-sortable',
                handle: '.link-drag-handle',
                opacity: 0.8,
                cursor: 'move',
                placeholder: 'link-sort-placeholder',
                helper: 'clone',
                tolerance: 'pointer',
                start: function(event, ui) {
                    ui.placeholder.html('<td colspan="11" style="height: 40px; background: #f0f8ff; border: 2px dashed #0073aa; text-align: center; vertical-align: middle;">Drop here</td>');
                },
                update: function(event, ui) {
                    var linkOrder = [];
                    $('#doolinks_response .link-row-sortable').each(function(index) {
                        var linkId = $(this).data('link-id');
                        linkOrder.push({
                            id: linkId,
                            order: index + 1
                        });
                    });
                    
                    // Save new order via AJAX
                    saveLinkOrder(linkOrder);
                }
            });
        }
    }
    
    // Arrow controls for moving up/down
    $(document).on('click', '.link-move-up', function(e) {
        e.preventDefault();
        var $row = $(this).closest('tr');
        var $prevRow = $row.prev('.link-row-sortable');
        
        if ($prevRow.length) {
            $row.insertBefore($prevRow);
            updateLinkOrder();
        }
    });
    
    $(document).on('click', '.link-move-down', function(e) {
        e.preventDefault();
        var $row = $(this).closest('tr');
        var $nextRow = $row.next('.link-row-sortable');
        
        if ($nextRow.length) {
            $row.insertAfter($nextRow);
            updateLinkOrder();
        }
    });
    
    // Update link order after arrow movement
    function updateLinkOrder() {
        var linkOrder = [];
        $('#doolinks_response .link-row-sortable').each(function(index) {
            var linkId = $(this).data('link-id');
            linkOrder.push({
                id: linkId,
                order: index + 1
            });
        });
        
        saveLinkOrder(linkOrder);
    }
    
    // Save link order via AJAX
    function saveLinkOrder(linkOrder) {
        $.ajax({
            url: (typeof ajaxurl !== 'undefined') ? ajaxurl : '/wp-admin/admin-ajax.php',
            type: 'POST',
            data: {
                action: 'save_links_order',
                links_order: linkOrder,
                nonce: 'temp_nonce' // We'll skip nonce for now like episode links
            },
            success: function(response) {
                console.log('Link order saved successfully');
                
                // Show temporary success indicator
                showOrderUpdateIndicator('success');
            },
            error: function() {
                console.log('Error saving link order');
                showOrderUpdateIndicator('error');
            }
        });
    }
    
    // Show visual feedback for order update
    function showOrderUpdateIndicator(type) {
        var message = type === 'success' ? 'Order updated!' : 'Error updating order';
        var color = type === 'success' ? '#46b450' : '#dc3232';
        
        var $indicator = $('<div class="link-order-indicator">' + message + '</div>');
        $indicator.css({
            position: 'fixed',
            top: '50px',
            right: '20px',
            background: color,
            color: 'white',
            padding: '10px 15px',
            borderRadius: '5px',
            zIndex: 9999,
            fontSize: '14px',
            fontWeight: 'bold'
        });
        
        $('body').append($indicator);
        
        setTimeout(function() {
            $indicator.fadeOut(300, function() {
                $(this).remove();
            });
        }, 2000);
    }
    
    // Initialize when page loads
    initLinksSortable();
    
    // Re-initialize after AJAX reload
    $(document).on('click', '#dooplay_anchor_reloadllist', function() {
        setTimeout(function() {
            initLinksSortable();
        }, 1000);
    });
    
    // Re-initialize after adding new links
    $(document).on('click', '#dooplay_anchor_postlinks', function() {
        setTimeout(function() {
            initLinksSortable();
        }, 1000);
    });
    
    // Re-initialize after episode links are saved
    $(document).on('click', '#save_all_episodes', function() {
        setTimeout(function() {
            initLinksSortable();
        }, 2000);
    });
    
    // Add CSS for sortable placeholder
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .link-sort-placeholder {
                background: #f0f8ff !important;
                border: 2px dashed #0073aa !important;
                height: 40px !important;
            }
            
            .link-row-sortable.ui-sortable-helper {
                background: #fff !important;
                box-shadow: 0 5px 15px rgba(0,0,0,0.3) !important;
                border: 1px solid #0073aa !important;
            }
            
            .link-reorder-controls {
                display: flex;
                gap: 5px;
                align-items: center;
                justify-content: center;
            }
            
            .link-drag-handle {
                cursor: move;
                color: #666;
                font-size: 16px;
                padding: 5px;
                border-radius: 3px;
                transition: all 0.3s ease;
            }
            
            .link-drag-handle:hover {
                background: #f0f0f0;
                color: #0073aa;
            }
            
            .link-arrow-controls {
                display: flex;
                flex-direction: column;
                gap: 2px;
            }
            
            .link-move-up,
            .link-move-down {
                cursor: pointer;
                color: #0073aa;
                font-size: 14px;
                padding: 2px;
                border-radius: 2px;
                transition: all 0.3s ease;
            }
            
            .link-move-up:hover,
            .link-move-down:hover {
                background: #0073aa;
                color: white;
            }
            
            .link-move-up:first-child {
                opacity: 0.5;
            }
            
            .link-move-down:last-child {
                opacity: 0.5;
            }
            
            /* Disable arrow if it's first/last row */
            tr:first-child .link-move-up {
                opacity: 0.3;
                cursor: not-allowed;
                pointer-events: none;
            }
            
            tr:last-child .link-move-down {
                opacity: 0.3;
                cursor: not-allowed;
                pointer-events: none;
            }
            
            /* Mobile responsive */
            @media (max-width: 768px) {
                .link-reorder-controls {
                    flex-direction: column;
                    gap: 3px;
                }
                
                .link-drag-handle {
                    font-size: 14px;
                }
                
                .link-arrow-controls {
                    flex-direction: row;
                    gap: 5px;
                }
            }
        `)
        .appendTo('head');
    
});

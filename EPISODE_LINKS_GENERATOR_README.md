# Episode Links Generator - Documentation

## Overview
এই নতুন feature টি আপনাকে একসাথে একাধিক episode এর download links যুক্ত করার সুবিধা দেয়। আপনি শুধু episode সংখ্যা select করলেই সেই অনুযায়ী EP-1, EP-2 এরকম সিরিয়াল সহ link fields তৈরি হয়ে যাবে।

## Features
- ১-৫০ পর্যন্ত episodes এর জন্য fields generate করা যায়
- প্রতিটি episode এর জন্য আলাদা download link
- সব episodes এর জন্য common Type, Language, Quality, File Size set করা যায়
- Real-time status indicator (Empty/Ready)
- Preview functionality
- Bulk save option
- Responsive design

## কীভাবে ব্যবহার করবেন

### ১. Episode Links Generator Section
- WordPress Admin → Movies/Episodes → Edit Post এ যান
- "Links" metabox এ "Episode Links Generator" section দেখতে পাবেন

### ২. Episode Fields Generate করা
1. **Episodes**: Dropdown থেকে কতটি episode এর link যুক্ত করবেন সেটি select করুন (১-৫০)
2. **Type**: Download/Stream/Torrent ইত্যাদি select করুন
3. **Language**: ভাষা select করুন (English, Bengali ইত্যাদি)
4. **Quality**: Video quality select করুন (HD 1080p, HD 720p ইত্যাদি)
5. **File Size**: Optional - ফাইলের সাইজ লিখুন
6. **"Generate Fields"** button এ click করুন

### ৩. Episode Links যুক্ত করা
- Generate করার পর প্রতিটি episode এর জন্য আলাদা field দেখতে পাবেন
- EP-1, EP-2, EP-3... এরকম label সহ
- প্রতিটি field এ সংশ্লিষ্ট episode এর download link paste করুন
- Status indicator দেখাবে কোন field Empty আর কোনটি Ready

### ৪. Preview এবং Save
- **"Preview Links"**: সব links এর একটি table preview দেখতে পাবেন
- **"Save All Episodes"**: সব episode links একসাথে save হয়ে যাবে
- **"Clear All"**: সব fields clear করে দিবে

## Technical Details

### Files Modified/Created:
1. `inc/parts/links_editor.php` - Main UI updated
2. `inc/doo_links.php` - AJAX handler added
3. `inc/doo_scripts.php` - Scripts enqueued
4. `dooplay/assets/js/episode-links-generator.js` - JavaScript functionality
5. `dooplay/assets/css/episode-links-generator.css` - Styling

### AJAX Endpoint:
- Action: `save_episode_links`
- Method: POST
- Security: Nonce verification + user capability check

### Database Storage:
- প্রতিটি episode একটি আলাদা `dt_links` post হিসেবে save হয়
- Post title: "EP-1", "EP-2" ইত্যাদি
- Meta fields: URL, Type, Language, Quality, Size

## Browser Compatibility
- Chrome, Firefox, Safari, Edge
- Mobile responsive
- jQuery dependent

## Security Features
- User capability check (`edit_posts`)
- Nonce verification
- Data sanitization
- XSS protection

## Troubleshooting

### যদি Episode Links Generator দেখা না যায়:
1. Post type Movies বা Episodes হতে হবে
2. Theme Options → Links Module → "Links Editor" enable করতে হবে
3. Browser cache clear করুন

### যদি Save না হয়:
1. User permission check করুন
2. Browser console এ error check করুন
3. WordPress debug log check করুন

### যদি JavaScript error আসে:
1. jQuery properly loaded আছে কিনা check করুন
2. Browser console এ specific error দেখুন
3. File permissions check করুন

## Future Enhancements
- Bulk URL import from text file
- Episode numbering customization
- Season-wise organization
- Auto-numbering based on existing episodes
- Import from external sources

## Support
কোন সমস্যা হলে:
1. Browser console check করুন
2. WordPress debug log enable করুন
3. File permissions verify করুন
4. Theme compatibility check করুন

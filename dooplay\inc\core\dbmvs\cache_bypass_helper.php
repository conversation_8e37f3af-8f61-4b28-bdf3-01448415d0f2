<?php
/**
 * Cache Bypass Helper for Development
 */

// Check if cache should be disabled
function dooplay_is_cache_disabled() {
    $disable_file = __DIR__ . "/.cache_disabled";
    return file_exists($disable_file) || defined("DOOPLAY_CACHE_DISABLED");
}

// Safe cache write function
function dooplay_safe_cache_write($file, $data) {
    if (dooplay_is_cache_disabled()) {
        return false; // Skip caching
    }
    
    try {
        return file_put_contents($file, $data, LOCK_EX);
    } catch (Exception $e) {
        error_log("Dooplay Cache Error: " . $e->getMessage());
        return false;
    }
}

// Safe cache read function
function dooplay_safe_cache_read($file) {
    if (dooplay_is_cache_disabled()) {
        return false; // Skip caching
    }
    
    if (!file_exists($file)) {
        return false;
    }
    
    try {
        return file_get_contents($file);
    } catch (Exception $e) {
        error_log("Dooplay Cache Error: " . $e->getMessage());
        return false;
    }
}

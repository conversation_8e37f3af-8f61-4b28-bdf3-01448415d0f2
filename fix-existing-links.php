<?php
/**
 * Fix Existing Links - Remove Episode Badge from Non-Episode Links
 * Run this script once to fix existing links
 */

// WordPress environment
require_once('../../../wp-config.php');

// Get all dt_links posts
$all_links = get_posts(array(
    'post_type' => 'dt_links',
    'posts_per_page' => -1,
    'post_status' => 'any'
));

$fixed_count = 0;
$episode_count = 0;

foreach($all_links as $link) {
    $link_title = $link->post_title;
    $is_episode_link = get_post_meta($link->ID, '_is_episode_link', true);
    
    // If it's not marked as episode link but has EP- title, it's probably old episode link
    if(!$is_episode_link && strpos($link_title, 'EP-') === 0) {
        // Mark as episode link
        update_post_meta($link->ID, '_is_episode_link', true);
        $episode_count++;
        echo "Marked as episode link: {$link_title} (ID: {$link->ID})\n";
    }
    // If it doesn't have EP- title, make sure it's not marked as episode link
    elseif($is_episode_link && strpos($link_title, 'EP-') !== 0) {
        // Remove episode link marker
        delete_post_meta($link->ID, '_is_episode_link');
        $fixed_count++;
        echo "Removed episode marker from: {$link_title} (ID: {$link->ID})\n";
    }
}

echo "\n=== SUMMARY ===\n";
echo "Total links processed: " . count($all_links) . "\n";
echo "Links marked as episodes: {$episode_count}\n";
echo "Links fixed (removed episode marker): {$fixed_count}\n";
echo "\nDone! You can now delete this file.\n";
?>

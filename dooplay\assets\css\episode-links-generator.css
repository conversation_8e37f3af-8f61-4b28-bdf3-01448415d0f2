/* Episode Links Generator Styles */

.episode-links-generator {
    background: #f9f9f9;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.episode-links-generator h4 {
    margin-top: 0;
    color: #0073aa;
    font-size: 16px;
    font-weight: 600;
}

.episode-links-generator table {
    width: 100%;
    border-collapse: collapse;
}

.episode-links-generator td {
    padding: 5px 10px 5px 0;
    vertical-align: top;
}

.episode-links-generator label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.episode-links-generator select,
.episode-links-generator input[type="text"] {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.episode-links-generator select:focus,
.episode-links-generator input[type="text"]:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.episode-links-form {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
}

.episode-links-form h4 {
    margin-top: 0;
    color: #0073aa;
    font-size: 16px;
    font-weight: 600;
}

.episode-links-grid {
    display: grid;
    gap: 15px;
}

.episode-row {
    border: 1px solid #e1e1e1;
    padding: 10px;
    border-radius: 3px;
    background: #f9f9f9;
    transition: all 0.3s ease;
}

.episode-row:hover {
    background: #f0f8ff;
    border-color: #0073aa;
}

.episode-row > div {
    display: grid;
    grid-template-columns: 80px 1fr 100px;
    gap: 10px;
    align-items: center;
}

.episode-row label {
    font-weight: bold;
    color: #333;
    font-size: 14px;
}

.episode-row input[type="text"] {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 13px;
}

.episode-row input[type="text"]:focus {
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
    outline: none;
}

.episode-status {
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    padding: 4px 8px;
    border-radius: 3px;
    background: #f1f1f1;
}

.episode-status[style*="color: #46b450"] {
    background: #d4edda;
    color: #155724 !important;
}

.episode-status[style*="color: #999"] {
    background: #f8f9fa;
    color: #6c757d !important;
}

/* Action buttons */
.episode-links-form .button {
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.4;
    border-radius: 3px;
    text-decoration: none;
    display: inline-block;
    cursor: pointer;
    border: 1px solid;
    margin: 0 5px 0 0;
}

.episode-links-form .button-primary {
    background: #0073aa;
    border-color: #0073aa;
    color: #fff;
}

.episode-links-form .button-primary:hover {
    background: #005a87;
    border-color: #005a87;
}

.episode-links-form .button-secondary {
    background: #f1f1f1;
    border-color: #ccc;
    color: #333;
}

.episode-links-form .button-secondary:hover {
    background: #e1e1e1;
    border-color: #999;
}

/* Preview table styles */
.episode-links-form table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.episode-links-form table th,
.episode-links-form table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.episode-links-form table th {
    background: #f1f1f1;
    font-weight: 600;
}

.episode-links-form table tr:nth-child(even) {
    background: #f9f9f9;
}

/* Responsive design */
@media (max-width: 768px) {
    .episode-links-generator table,
    .episode-links-generator tbody,
    .episode-links-generator tr,
    .episode-links-generator td {
        display: block;
        width: 100%;
    }
    
    .episode-links-generator td {
        padding: 10px 0;
        border-bottom: 1px solid #eee;
    }
    
    .episode-row > div {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .episode-row label {
        margin-bottom: 5px;
    }
}

/* Loading state */
.episode-links-form .button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Success/Error messages */
.episode-message {
    padding: 10px 15px;
    margin: 10px 0;
    border-radius: 3px;
    font-size: 14px;
}

.episode-message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.episode-message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Animation for new fields */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.episode-row {
    animation: fadeInUp 0.3s ease-out;
}

/* Improved form layout */
.dform h4 {
    color: #0073aa;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

/* Better spacing */
.episode-links-generator + .episode-links-form {
    margin-top: 0;
}

#episode_fields_container + .dform {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

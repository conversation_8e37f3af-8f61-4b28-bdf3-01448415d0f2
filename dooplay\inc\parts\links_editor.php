<div class="dooplay_links">
    <!-- Episode Links Generator Section -->
    <div class="episode-links-generator" style="background: #f9f9f9; padding: 15px; margin-bottom: 20px; border: 1px solid #ddd; border-radius: 5px;">
        <h4 style="margin-top: 0;"><?php _d('Episode Links Generator'); ?></h4>
        <table style="width: 100%;">
            <tbody>
                <tr>
                    <td style="width: 15%;">
                        <label for="episode_count"><?php _d('Episodes'); ?>:</label>
                        <select id="episode_count" name="episode_count" style="width: 100%;">
                            <option value=""><?php _d('Select Episodes'); ?></option>
                            <?php for($i = 1; $i <= 50; $i++): ?>
                                <option value="<?php echo $i; ?>"><?php echo $i; ?> <?php _d('Episodes'); ?></option>
                            <?php endfor; ?>
                        </select>
                    </td>
                    <td style="width: 15%;">
                        <label for="episode_type"><?php _d('Type'); ?>:</label>
                        <select id='episode_type' name='episode_type' style="width: 100%;">
                            <?php foreach( $this->types() as $type) { echo "<option>{$type}</option>"; } ?>
                        </select>
                    </td>
                    <td style="width: 15%;">
                        <label for="episode_lang"><?php _d('Language'); ?>:</label>
                        <select id='episode_lang' name='episode_lang' style="width: 100%;">
                            <?php foreach( $this->langs() as $type) { echo "<option>{$type}</option>"; } ?>
                        </select>
                    </td>
                    <td style="width: 15%;">
                        <label for="episode_quality"><?php _d('Quality'); ?>:</label>
                        <select id='episode_quality' name='episode_quality' style="width: 100%;">
                            <?php foreach( $this->resolutions() as $type) { echo "<option>{$type}</option>"; } ?>
                        </select>
                    </td>
                    <td style="width: 15%;">
                        <label for="episode_size"><?php _d('File size'); ?>:</label>
                        <input type="text" id="episode_size" name="episode_size" placeholder="<?php _d('File size'); ?>" style="width: 100%;"/>
                    </td>
                    <td style="width: 25%;">
                        <label>&nbsp;</label><br>
                        <button type="button" id="generate_episode_fields" class="button button-primary"><?php _d('Generate Fields'); ?></button>
                        <button type="button" id="clear_episode_fields" class="button button-secondary"><?php _d('Clear All'); ?></button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- Generated Episode Fields Container -->
    <div id="episode_fields_container" style="margin-bottom: 20px;"></div>

    <!-- Original Single Link Form -->
    <div class="dform">
        <h4><?php _d('Add Single Link'); ?></h4>
        <fieldset>
            <table>
                <tbody>
                    <tr>
                        <td>
                            <select id='dooplay_lfield_type' name='dooplay_lfield_type'>
                                <?php foreach( $this->types() as $type) { echo "<option>{$type}</option>"; } ?>
                            </select>
                        </td>
                        <td>
                            <select id='dooplay_lfield_lang' name='dooplay_lfield_lang'>
                                <?php foreach( $this->langs() as $type) { echo "<option>{$type}</option>"; } ?>
                            </select>
                        </td>
                        <td>
                            <select id='dooplay_lfield_qual' name='dooplay_lfield_qual'>
                                <?php foreach( $this->resolutions() as $type) { echo "<option>{$type}</option>"; } ?>
                            </select>
                        </td>
                        <td>
                            <input type="text" id="dooplay_lfield_size" name="dooplay_lfield_size" placeholder="<?php _d('File size'); ?>"/>
                        </td>
                    </tr>
                </tbody>
            </table>
        </fieldset>
        <fieldset>
            <textarea id="dooplay_lfield_urls" name="dooplay_lfield_urls" rows="3" placeholder="<?php _d('Add a link per line'); ?>"></textarea>
        </fieldset>
        <fieldset>
            <a href="#" id="dooplay_anchor_hideform" class="button button-decundary"><?php _d('Cancel'); ?></a>
            <a href="#" id="dooplay_anchor_postlinks" class="button button-primary right"><?php _d('Add Links'); ?></a>
        </fieldset>
    </div>
    <div class="dpre">
        <a href="#" id="dooplay_anchor_showform" class="button button-primary"><?php _d('Add Links'); ?></a>
        <a href="#" id="dooplay_anchor_reloadllist" class="button button-secundary right" data-id="<?php echo $post->ID; ?>"><?php _d('Reload List'); ?></a>
    </div>
    <table>
        <thead>
            <tr>
                <th><?php _d('Type'); ?></th>
                <th><?php _d('Server'); ?></th>
                <th><?php _d('Language'); ?></th>
                <th><?php _d('Quality'); ?></th>
                <th><?php _d('Size'); ?></th>
                <th><?php _d('Clicks'); ?></th>
                <th><?php _d('User'); ?></th>
                <th><?php _d('Added'); ?></th>
                <th colspan="2"><?php _d('Manage'); ?></th>
            </tr>
        </thead>
        <tbody id="doolinks_response">
            <?php $this->tablelist_admin($post->ID); ?>
        </tbody>
    </table>
</div>
